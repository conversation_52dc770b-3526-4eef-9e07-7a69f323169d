using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Infrastructure.Data;
using WorkflowEngine.Infrastructure.Repositories;
using WorkflowEngine.Infrastructure.Services;

namespace WorkflowEngine.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        services.AddDbContext<WorkflowDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Tenant Context
        services.AddScoped<ITenantContext, TenantContext>();
        services.AddScoped<ITenantResolver, TenantResolver>();
        services.AddScoped<ITenantService, TenantService>();

        // Repositories
        services.AddScoped<ITenantRepository, TenantRepository>();
        services.AddScoped<IWorkflowDefinitionRepository, WorkflowDefinitionRepository>();
        services.AddScoped<IWorkflowInstanceRepository, WorkflowInstanceRepository>();
        services.AddScoped<IActivityRepository, ActivityRepository>();
        services.AddScoped<IActivityExecutionRepository, ActivityExecutionRepository>();

        // Workflow Services
        services.AddScoped<IWorkflowEngine, Services.WorkflowEngine>();
        services.AddScoped<IWorkflowVariableService, WorkflowVariableService>();
        services.AddSingleton<IActivityExecutorFactory, ActivityExecutorFactory>();

        // HTTP Context Accessor for tenant resolution
        services.AddHttpContextAccessor();

        return services;
    }
}
