using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using WorkflowEngine.Application.Interfaces;
using WorkflowEngine.Domain.Enums;

namespace WorkflowEngine.Infrastructure.Services;

public class WorkflowBackgroundProcessor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowBackgroundProcessor> _logger;
    private readonly TimeSpan _processingInterval = TimeSpan.FromSeconds(30); // Process every 30 seconds
    private readonly TimeSpan _retryInterval = TimeSpan.FromMinutes(1); // Retry failed items every minute

    public WorkflowBackgroundProcessor(
        IServiceProvider serviceProvider,
        ILogger<WorkflowBackgroundProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Workflow Background Processor started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessWorkflowsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in workflow background processor");
            }

            try
            {
                await Task.Delay(_processingInterval, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
        }

        _logger.LogInformation("Workflow Background Processor stopped");
    }

    private async Task ProcessWorkflowsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var workflowInstanceRepository = scope.ServiceProvider.GetRequiredService<IWorkflowInstanceRepository>();
        var activityExecutionRepository = scope.ServiceProvider.GetRequiredService<IActivityExecutionRepository>();
        var workflowEngine = scope.ServiceProvider.GetRequiredService<IWorkflowEngine>();

        var processedCount = 0;

        try
        {
            // Process pending workflow instances
            var pendingInstances = await workflowInstanceRepository.GetPendingExecutionAsync(DateTime.UtcNow, cancellationToken);
            
            foreach (var instance in pendingInstances)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogDebug("Processing pending workflow instance {InstanceId}", instance.Id);
                    
                    var result = await workflowEngine.ProgressInstanceAsync(instance.Id, cancellationToken);
                    
                    if (result.IsSuccess)
                    {
                        _logger.LogDebug("Successfully processed workflow instance {InstanceId}", instance.Id);
                        processedCount++;
                    }
                    else
                    {
                        _logger.LogWarning("Failed to process workflow instance {InstanceId}: {Error}", 
                            instance.Id, result.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing workflow instance {InstanceId}", instance.Id);
                }
            }

            // Process failed instances for retry
            var failedInstances = await workflowInstanceRepository.GetInstancesForRetryAsync(DateTime.UtcNow, cancellationToken);
            
            foreach (var instance in failedInstances)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogDebug("Retrying failed workflow instance {InstanceId} (attempt {RetryCount})", 
                        instance.Id, instance.RetryCount + 1);
                    
                    var retryResult = await workflowEngine.RetryInstanceAsync(instance.Id, cancellationToken);
                    var progressResult = await workflowEngine.ProgressInstanceAsync(instance.Id, cancellationToken);
                    
                    if (progressResult.IsSuccess)
                    {
                        _logger.LogInformation("Successfully retried workflow instance {InstanceId}", instance.Id);
                        processedCount++;
                    }
                    else
                    {
                        _logger.LogWarning("Retry failed for workflow instance {InstanceId}: {Error}", 
                            instance.Id, progressResult.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error retrying workflow instance {InstanceId}", instance.Id);
                }
            }

            // Process failed activity executions for retry
            var failedExecutions = await activityExecutionRepository.GetFailedExecutionsForRetryAsync(DateTime.UtcNow, cancellationToken);
            
            foreach (var execution in failedExecutions)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    _logger.LogDebug("Retrying failed activity execution {ExecutionId} (attempt {RetryCount})", 
                        execution.Id, execution.RetryCount + 1);
                    
                    await ProcessActivityExecutionRetryAsync(execution, scope.ServiceProvider, cancellationToken);
                    processedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error retrying activity execution {ExecutionId}", execution.Id);
                }
            }

            if (processedCount > 0)
            {
                _logger.LogInformation("Processed {Count} workflow items in background processor", processedCount);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in workflow background processing cycle");
        }
    }

    private async Task ProcessActivityExecutionRetryAsync(
        Domain.Entities.ActivityExecution execution, 
        IServiceProvider serviceProvider, 
        CancellationToken cancellationToken)
    {
        var activityExecutionRepository = serviceProvider.GetRequiredService<IActivityExecutionRepository>();
        var activityExecutorFactory = serviceProvider.GetRequiredService<IActivityExecutorFactory>();
        var workflowInstanceRepository = serviceProvider.GetRequiredService<IWorkflowInstanceRepository>();
        var workflowDefinitionRepository = serviceProvider.GetRequiredService<IWorkflowDefinitionRepository>();
        var activityRepository = serviceProvider.GetRequiredService<IActivityRepository>();
        var variableService = serviceProvider.GetRequiredService<IWorkflowVariableService>();

        try
        {
            // Get related entities
            var workflowInstance = await workflowInstanceRepository.GetByIdAsync(execution.TenantId, execution.WorkflowInstanceId, cancellationToken);
            if (workflowInstance == null)
            {
                _logger.LogWarning("Workflow instance {InstanceId} not found for activity execution {ExecutionId}", 
                    execution.WorkflowInstanceId, execution.Id);
                return;
            }

            var workflowDefinition = await workflowDefinitionRepository.GetByIdAsync(execution.TenantId, workflowInstance.WorkflowDefinitionId, cancellationToken);
            if (workflowDefinition == null)
            {
                _logger.LogWarning("Workflow definition {DefinitionId} not found for activity execution {ExecutionId}", 
                    workflowInstance.WorkflowDefinitionId, execution.Id);
                return;
            }

            var workflowStep = workflowDefinition.Steps.FirstOrDefault(s => s.Id == execution.WorkflowStepId);
            if (workflowStep == null)
            {
                _logger.LogWarning("Workflow step {StepId} not found for activity execution {ExecutionId}", 
                    execution.WorkflowStepId, execution.Id);
                return;
            }

            var activity = await activityRepository.GetByIdAsync(execution.TenantId, execution.ActivityId, cancellationToken);
            if (activity == null)
            {
                _logger.LogWarning("Activity {ActivityId} not found for activity execution {ExecutionId}", 
                    execution.ActivityId, execution.Id);
                return;
            }

            // Get activity executor
            var executor = activityExecutorFactory.GetExecutor(activity.Type);
            if (executor == null)
            {
                _logger.LogWarning("No executor found for activity type {ActivityType}", activity.Type);
                
                // Mark as failed permanently
                execution.Status = ExecutionStatus.Failed;
                execution.ErrorMessage = $"No executor available for activity type {activity.Type}";
                execution.CompletedAt = DateTime.UtcNow;
                
                await activityExecutionRepository.UpdateAsync(execution, cancellationToken);
                await activityExecutionRepository.SaveChangesAsync(cancellationToken);
                return;
            }

            // Get variables
            var variables = await variableService.GetAllVariablesAsync(workflowInstance.Id, cancellationToken);

            // Create execution context
            var context = new ActivityExecutionContext
            {
                Activity = activity,
                WorkflowInstance = workflowInstance,
                WorkflowStep = workflowStep,
                InputData = execution.InputData,
                Variables = variables,
                RetryCount = execution.RetryCount,
                CancellationToken = cancellationToken
            };

            // Update execution status
            execution.Status = ExecutionStatus.Retrying;
            execution.RetryCount++;
            execution.StartedAt = DateTime.UtcNow;
            
            await activityExecutionRepository.UpdateAsync(execution, cancellationToken);
            await activityExecutionRepository.SaveChangesAsync(cancellationToken);

            // Execute activity
            var result = await executor.ExecuteAsync(context, cancellationToken);

            // Update execution with result
            execution.Status = result.Status;
            execution.OutputData = result.OutputData;
            execution.ErrorMessage = result.ErrorMessage;
            execution.StackTrace = result.StackTrace;
            execution.CompletedAt = DateTime.UtcNow;
            execution.Duration = DateTime.UtcNow - execution.StartedAt;

            if (result.ShouldRetry && execution.RetryCount < execution.MaxRetries)
            {
                execution.NextRetryAt = result.NextRetryAt ?? DateTime.UtcNow.Add(_retryInterval);
            }
            else
            {
                execution.NextRetryAt = null;
            }

            await activityExecutionRepository.UpdateAsync(execution, cancellationToken);
            await activityExecutionRepository.SaveChangesAsync(cancellationToken);

            // Update variables if provided
            if (result.Variables != null && result.Variables.Any())
            {
                await variableService.MergeVariablesAsync(workflowInstance.Id, result.Variables, cancellationToken);
            }

            _logger.LogInformation("Activity execution {ExecutionId} retry completed with status {Status}", 
                execution.Id, result.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during activity execution retry {ExecutionId}", execution.Id);
            
            // Update execution with error
            execution.Status = ExecutionStatus.Failed;
            execution.ErrorMessage = $"Retry failed: {ex.Message}";
            execution.StackTrace = ex.StackTrace;
            execution.CompletedAt = DateTime.UtcNow;
            execution.RetryCount++;

            if (execution.RetryCount < execution.MaxRetries)
            {
                execution.NextRetryAt = DateTime.UtcNow.Add(_retryInterval);
            }

            await activityExecutionRepository.UpdateAsync(execution, cancellationToken);
            await activityExecutionRepository.SaveChangesAsync(cancellationToken);
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Workflow Background Processor is stopping");
        await base.StopAsync(cancellationToken);
    }
}
